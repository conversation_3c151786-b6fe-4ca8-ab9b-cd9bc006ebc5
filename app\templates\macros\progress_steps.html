{% macro render_progress_steps(current_step, total_steps, steps, title="流程进度") %}
<!-- 通用步骤进度条组件 -->
<div class="mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
        <small class="text-muted">{{ title }}</small>
        <small class="text-muted">步骤 {{ current_step }}/{{ total_steps }}</small>
    </div>
    <div class="progress" style="height: 8px;">
        <div class="progress-bar bg-success" 
             role="progressbar" 
             style="width: {{ (current_step / total_steps * 100)|round(1) }}%" 
             aria-valuenow="{{ (current_step / total_steps * 100)|round(1) }}" 
             aria-valuemin="0" 
             aria-valuemax="100">
        </div>
    </div>
    <div class="d-flex justify-content-between mt-2 flex-wrap">
        {% for step in steps %}
        <small class="{% if loop.index <= current_step %}text-success{% if loop.index == current_step %} font-weight-bold{% endif %}{% else %}text-muted{% endif %} mb-1">
            {{ loop.index }}. {{ step }}
        </small>
        {% endfor %}
    </div>
</div>
{% endmacro %}

{% macro render_responsive_progress_steps(current_step, total_steps, steps, title="流程进度") %}
<!-- 响应式步骤进度条组件 -->
<div class="mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
        <small class="text-muted">{{ title }}</small>
        <small class="text-muted">步骤 {{ current_step }}/{{ total_steps }}</small>
    </div>
    <div class="progress" style="height: 8px;">
        <div class="progress-bar bg-success" 
             role="progressbar" 
             style="width: {{ (current_step / total_steps * 100)|round(1) }}%" 
             aria-valuenow="{{ (current_step / total_steps * 100)|round(1) }}" 
             aria-valuemin="0" 
             aria-valuemax="100">
        </div>
    </div>
    
    <!-- 桌面版步骤显示 -->
    <div class="d-none d-md-flex justify-content-between mt-2">
        {% for step in steps %}
        <small class="{% if loop.index <= current_step %}text-success{% if loop.index == current_step %} font-weight-bold{% endif %}{% else %}text-muted{% endif %}">
            {{ loop.index }}. {{ step }}
        </small>
        {% endfor %}
    </div>
    
    <!-- 移动版步骤显示 -->
    <div class="d-md-none mt-2">
        <div class="row">
            {% for step in steps %}
            <div class="col-6 col-sm-4 mb-1">
                <small class="{% if loop.index <= current_step %}text-success{% if loop.index == current_step %} font-weight-bold{% endif %}{% else %}text-muted{% endif %}">
                    {{ loop.index }}. {{ step }}
                </small>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endmacro %}

{% macro render_compact_progress_steps(current_step, total_steps, steps, title="流程进度") %}
<!-- 紧凑型步骤进度条组件 -->
<div class="mb-3">
    <div class="d-flex justify-content-between align-items-center mb-1">
        <small class="text-muted font-weight-bold">{{ title }}</small>
        <small class="text-muted">{{ current_step }}/{{ total_steps }}</small>
    </div>
    <div class="progress" style="height: 6px;">
        <div class="progress-bar bg-success" 
             role="progressbar" 
             style="width: {{ (current_step / total_steps * 100)|round(1) }}%" 
             aria-valuenow="{{ (current_step / total_steps * 100)|round(1) }}" 
             aria-valuemin="0" 
             aria-valuemax="100">
        </div>
    </div>
    <div class="mt-1">
        <small class="{% if current_step <= total_steps %}text-success font-weight-bold{% else %}text-muted{% endif %}">
            当前：{{ steps[current_step - 1] if current_step <= steps|length else '完成' }}
        </small>
        {% if current_step < total_steps %}
        <small class="text-muted ml-2">
            下一步：{{ steps[current_step] if current_step < steps|length else '完成' }}
        </small>
        {% endif %}
    </div>
</div>
{% endmacro %}

{% macro render_circular_progress_steps(current_step, total_steps, steps, title="流程进度") %}
<!-- 圆形步骤进度条组件 -->
<div class="mb-4">
    <div class="text-center mb-3">
        <h6 class="text-muted">{{ title }}</h6>
        <div class="d-flex justify-content-center align-items-center">
            {% for step in steps %}
            <div class="d-flex flex-column align-items-center mx-2">
                <div class="rounded-circle d-flex align-items-center justify-content-center mb-1
                           {% if loop.index <= current_step %}bg-success text-white{% else %}bg-light text-muted{% endif %}"
                     style="width: 30px; height: 30px; font-size: 12px; font-weight: bold;">
                    {{ loop.index }}
                </div>
                <small class="text-center {% if loop.index <= current_step %}text-success{% if loop.index == current_step %} font-weight-bold{% endif %}{% else %}text-muted{% endif %}"
                       style="max-width: 60px; line-height: 1.2;">
                    {{ step }}
                </small>
            </div>
            {% if not loop.last %}
            <div class="flex-grow-1 mx-1" style="height: 2px; background-color: {% if loop.index < current_step %}#28a745{% else %}#dee2e6{% endif %}; max-width: 30px;"></div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
</div>
{% endmacro %}

{% macro render_breadcrumb_progress_steps(current_step, total_steps, steps, title="流程进度") %}
<!-- 面包屑样式步骤进度条组件 -->
<div class="mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
        <small class="text-muted font-weight-bold">{{ title }}</small>
        <small class="text-muted">{{ current_step }}/{{ total_steps }}</small>
    </div>
    
    <nav aria-label="步骤导航">
        <ol class="breadcrumb mb-0" style="background-color: transparent; padding: 0;">
            {% for step in steps %}
            <li class="breadcrumb-item {% if loop.index == current_step %}active{% endif %}
                       {% if loop.index <= current_step %}text-success{% else %}text-muted{% endif %}">
                {% if loop.index < current_step %}
                <i class="fas fa-check-circle mr-1"></i>
                {% elif loop.index == current_step %}
                <i class="fas fa-circle mr-1"></i>
                {% else %}
                <i class="far fa-circle mr-1"></i>
                {% endif %}
                {{ step }}
            </li>
            {% endfor %}
        </ol>
    </nav>
    
    <div class="progress mt-2" style="height: 4px;">
        <div class="progress-bar bg-success" 
             role="progressbar" 
             style="width: {{ (current_step / total_steps * 100)|round(1) }}%" 
             aria-valuenow="{{ (current_step / total_steps * 100)|round(1) }}" 
             aria-valuemin="0" 
             aria-valuemax="100">
        </div>
    </div>
</div>
{% endmacro %}
