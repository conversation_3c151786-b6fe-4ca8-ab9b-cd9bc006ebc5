#!/usr/bin/env python3
"""
测试产品批次创建功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import IngredientCategory, Supplier
from app.utils.batch_number_generator import generate_batch_number, BatchNumberGenerator

def test_batch_creation():
    """测试产品批次创建功能"""
    app = create_app()
    
    with app.app_context():
        print("=== 产品批次创建功能测试 ===\n")
        
        # 1. 测试批次号生成
        print("1. 测试批次号生成...")
        try:
            batch_number = generate_batch_number()
            print(f"   ✅ 生成的批次号: {batch_number}")
            
            # 验证格式：PB + 8位日期 + 6位随机数 = 16位
            if len(batch_number) == 16 and batch_number.startswith('PB'):
                print("   ✅ 批次号格式正确")
            else:
                print(f"   ❌ 批次号格式错误，长度: {len(batch_number)}")
                return False
                
        except Exception as e:
            print(f"   ❌ 批次号生成失败: {e}")
            return False
        
        # 2. 测试显示名称生成
        print("\n2. 测试显示名称生成...")
        try:
            # 获取测试数据
            category = IngredientCategory.query.first()
            supplier = Supplier.query.first()
            
            if not category or not supplier:
                print("   ❌ 缺少测试数据（分类或供应商）")
                return False
            
            display_name = BatchNumberGenerator.generate_display_name(
                batch_number,
                category.name,
                supplier.name
            )
            
            print(f"   ✅ 分类: {category.name}")
            print(f"   ✅ 供应商: {supplier.name}")
            print(f"   ✅ 生成的显示名称: {display_name}")
            
        except Exception as e:
            print(f"   ❌ 显示名称生成失败: {e}")
            return False
        
        # 3. 测试完整的创建流程模拟
        print("\n3. 测试完整的创建流程模拟...")
        try:
            # 模拟表单数据
            form_data = {
                'category_id': category.id,
                'supplier_id': supplier.id
            }
            
            # 生成批次号
            batch_number = generate_batch_number(
                category_id=form_data['category_id'],
                supplier_id=form_data['supplier_id']
            )
            
            # 生成显示名称
            display_name = BatchNumberGenerator.generate_display_name(
                batch_number,
                category.name,
                supplier.name
            )
            
            print(f"   ✅ 模拟表单数据:")
            print(f"   - 分类ID: {form_data['category_id']}")
            print(f"   - 供应商ID: {form_data['supplier_id']}")
            print(f"   ✅ 生成结果:")
            print(f"   - 批次号: {batch_number}")
            print(f"   - 显示名称: {display_name}")
            
        except Exception as e:
            print(f"   ❌ 创建流程模拟失败: {e}")
            return False
        
        # 4. 测试多次生成的唯一性
        print("\n4. 测试多次生成的唯一性...")
        try:
            batch_numbers = set()
            for i in range(3):
                bn = generate_batch_number()
                batch_numbers.add(bn)
                print(f"   - 第{i+1}次: {bn}")
            
            if len(batch_numbers) == 3:
                print("   ✅ 生成的批次号都是唯一的")
            else:
                print("   ❌ 发现重复的批次号")
                return False
                
        except Exception as e:
            print(f"   ❌ 唯一性测试失败: {e}")
            return False
        
        # 5. 测试日期解析
        print("\n5. 测试日期解析...")
        try:
            from datetime import datetime
            
            # 从批次号中提取日期
            date_part = batch_number[2:10]  # PB20240528123456 -> 20240528
            date_obj = datetime.strptime(date_part, '%Y%m%d')
            formatted_date = date_obj.strftime('%Y年%m月%d日')
            
            print(f"   ✅ 批次号: {batch_number}")
            print(f"   ✅ 日期部分: {date_part}")
            print(f"   ✅ 解析后的日期: {formatted_date}")
            
        except Exception as e:
            print(f"   ❌ 日期解析失败: {e}")
            return False
        
        print("\n=== 测试完成 ===")
        print("✅ 所有测试通过！产品批次创建功能正常。")
        print(f"✅ 批次号格式: PB + 日期(8位) + 随机数(6位)")
        print(f"✅ 示例: {batch_number}")
        print(f"✅ 显示名称: {display_name}")
        return True

if __name__ == '__main__':
    success = test_batch_creation()
    sys.exit(0 if success else 1)
