#!/usr/bin/env python3
"""
测试供应商编辑页面的权限过滤功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Supplier, SupplierSchoolRelation, AdministrativeArea, User

def test_supplier_edit_permission():
    """测试供应商编辑页面的权限过滤功能"""
    app = create_app()

    with app.app_context():
        print("=== 供应商编辑权限过滤测试 ===\n")

        # 1. 查找一个有多个学校关系的供应商
        print("1. 查找测试供应商...")
        try:
            # 查找有多个学校关系的供应商
            supplier = Supplier.query.join(SupplierSchoolRelation).filter(
                SupplierSchoolRelation.status == 1
            ).first()

            if not supplier:
                print("   ❌ 没有找到有学校关系的供应商")
                return False

            print(f"   ✅ 找到测试供应商: {supplier.name} (ID: {supplier.id})")

            # 显示所有学校关系
            all_relations = supplier.school_relations
            print(f"   ✅ 该供应商总共有 {len(all_relations)} 个学校关系")

            for relation in all_relations:
                print(f"   - 学校: {relation.area.name} (ID: {relation.area_id})")
                print(f"     合同: {relation.contract_number}")
                print(f"     状态: {'有效' if relation.status == 1 else '已终止'}")

        except Exception as e:
            print(f"   ❌ 查找供应商失败: {e}")
            return False

        # 2. 测试管理员权限（模拟）
        print("\n2. 测试管理员权限（模拟）...")
        try:
            # 模拟管理员权限过滤逻辑
            # 管理员可以看到所有学校关系
            admin_accessible_relations = supplier.school_relations

            print(f"   ✅ 管理员可以看到 {len(admin_accessible_relations)} 个学校关系")

        except Exception as e:
            print(f"   ❌ 管理员权限测试失败: {e}")
            return False

        # 3. 测试普通用户权限
        print("\n3. 测试普通用户权限...")
        try:
            # 查找任意用户
            normal_user = User.query.first()
            if not normal_user:
                print("   ❌ 没有找到用户")
                return False

            print(f"   ✅ 找到用户: {normal_user.username}")
            print(f"   ✅ 用户是否为管理员: {normal_user.is_admin()}")

            # 获取用户可访问的区域
            accessible_areas = normal_user.get_accessible_areas()
            accessible_area_ids = [area.id for area in accessible_areas]
            print(f"   ✅ 用户可访问的区域数量: {len(accessible_areas)}")

            for area in accessible_areas[:3]:  # 只显示前3个
                print(f"   - 区域: {area.name} (ID: {area.id})")

            # 模拟普通用户权限过滤逻辑
            if normal_user.is_admin():
                user_accessible_relations = supplier.school_relations
            else:
                user_accessible_relations = [
                    relation for relation in supplier.school_relations
                    if relation.area_id in accessible_area_ids
                ]

            print(f"   ✅ 用户可以看到 {len(user_accessible_relations)} 个学校关系")

            for relation in user_accessible_relations:
                print(f"   - 可见学校: {relation.area.name}")
                print(f"     合同: {relation.contract_number}")

        except Exception as e:
            print(f"   ❌ 用户权限测试失败: {e}")
            return False

        # 4. 验证权限过滤逻辑
        print("\n4. 验证权限过滤逻辑...")
        try:
            # 检查管理员和普通用户看到的关系数量差异
            admin_count = len(admin_accessible_relations)
            user_count = len(user_accessible_relations)

            print(f"   ✅ 管理员可见关系数: {admin_count}")
            print(f"   ✅ 普通用户可见关系数: {user_count}")

            if admin_count >= user_count:
                print("   ✅ 权限过滤逻辑正确：管理员可见关系数 >= 普通用户可见关系数")
            else:
                print("   ❌ 权限过滤逻辑异常：管理员可见关系数 < 普通用户可见关系数")
                return False

            # 验证普通用户只能看到自己区域的关系
            for relation in user_accessible_relations:
                if relation.area_id not in accessible_area_ids:
                    print(f"   ❌ 权限过滤失败：用户看到了不属于自己区域的学校关系 {relation.area.name}")
                    return False

            print("   ✅ 普通用户权限过滤正确：只能看到自己区域的学校关系")

        except Exception as e:
            print(f"   ❌ 权限过滤逻辑验证失败: {e}")
            return False

        print("\n=== 测试完成 ===")
        print("✅ 所有测试通过！供应商编辑页面权限过滤功能正常工作。")
        print(f"✅ 测试的供应商ID: {supplier.id}")
        print(f"✅ 管理员可见关系: {len(admin_accessible_relations)} 个")
        print(f"✅ 普通用户可见关系: {len(user_accessible_relations)} 个")
        return True

if __name__ == '__main__':
    success = test_supplier_edit_permission()
    sys.exit(0 if success else 1)
