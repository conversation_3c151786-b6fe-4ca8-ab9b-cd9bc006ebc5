#!/usr/bin/env python3
"""
测试批次号生成器功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Supplier, IngredientCategory, User
from app.utils.batch_number_generator import BatchNumberGenerator, generate_batch_number, validate_batch_number, parse_batch_number

def test_batch_number_generator():
    """测试批次号生成器功能"""
    app = create_app()
    
    with app.app_context():
        print("=== 批次号生成器测试 ===\n")
        
        # 1. 测试基础数据
        print("1. 查找测试数据...")
        try:
            # 查找分类
            category = IngredientCategory.query.first()
            if not category:
                print("   ❌ 没有找到食材分类")
                return False
            
            # 查找供应商
            supplier = Supplier.query.first()
            if not supplier:
                print("   ❌ 没有找到供应商")
                return False
            
            # 查找用户
            user = User.query.first()
            if not user:
                print("   ❌ 没有找到用户")
                return False
            
            print(f"   ✅ 测试分类: {category.name} (ID: {category.id})")
            print(f"   ✅ 测试供应商: {supplier.name} (ID: {supplier.id})")
            print(f"   ✅ 测试用户: {user.username} (ID: {user.id})")
            
        except Exception as e:
            print(f"   ❌ 查找测试数据失败: {e}")
            return False
        
        # 2. 测试批次号生成
        print("\n2. 测试批次号生成...")
        try:
            # 生成批次号
            batch_number = generate_batch_number(
                category_id=category.id,
                supplier_id=supplier.id,
                user_id=user.id
            )
            
            print(f"   ✅ 生成的批次号: {batch_number}")
            
            # 验证批次号格式
            if validate_batch_number(batch_number):
                print("   ✅ 批次号格式验证通过")
            else:
                print("   ❌ 批次号格式验证失败")
                return False
            
        except Exception as e:
            print(f"   ❌ 批次号生成失败: {e}")
            return False
        
        # 3. 测试批次号解析
        print("\n3. 测试批次号解析...")
        try:
            parsed = parse_batch_number(batch_number)
            
            if parsed:
                print("   ✅ 批次号解析成功:")
                print(f"   - 前缀: {parsed['prefix']}")
                print(f"   - 日期: {parsed['date']}")
                print(f"   - 分类代码: {parsed['category_code']}")
                print(f"   - 供应商代码: {parsed['supplier_code']}")
                print(f"   - 供应商ID: {parsed['supplier_id']}")
                print(f"   - 序号: {parsed['sequence']}")
            else:
                print("   ❌ 批次号解析失败")
                return False
            
        except Exception as e:
            print(f"   ❌ 批次号解析失败: {e}")
            return False
        
        # 4. 测试显示名称生成
        print("\n4. 测试显示名称生成...")
        try:
            display_name = BatchNumberGenerator.generate_display_name(
                batch_number,
                category.name,
                supplier.name
            )
            
            print(f"   ✅ 生成的显示名称: {display_name}")
            
        except Exception as e:
            print(f"   ❌ 显示名称生成失败: {e}")
            return False
        
        # 5. 测试分类代码生成
        print("\n5. 测试分类代码生成...")
        try:
            category_code = BatchNumberGenerator._get_category_code(category.id)
            print(f"   ✅ 分类 '{category.name}' 的代码: {category_code}")
            
            # 测试常见分类
            test_categories = ['蔬菜', '肉类', '水果', '粮油', '调料']
            for cat_name in test_categories:
                test_cat = IngredientCategory.query.filter_by(name=cat_name).first()
                if test_cat:
                    code = BatchNumberGenerator._get_category_code(test_cat.id)
                    print(f"   - {cat_name}: {code}")
            
        except Exception as e:
            print(f"   ❌ 分类代码生成测试失败: {e}")
            return False
        
        # 6. 测试供应商代码生成
        print("\n6. 测试供应商代码生成...")
        try:
            supplier_code = BatchNumberGenerator._get_supplier_code(supplier.id)
            print(f"   ✅ 供应商 '{supplier.name}' (ID: {supplier.id}) 的代码: {supplier_code}")
            
            # 测试不同ID的供应商代码
            test_ids = [1, 5, 10, 99, 123]
            for test_id in test_ids:
                code = BatchNumberGenerator._get_supplier_code(test_id)
                print(f"   - 供应商ID {test_id}: {code}")
            
        except Exception as e:
            print(f"   ❌ 供应商代码生成测试失败: {e}")
            return False
        
        # 7. 测试序号生成
        print("\n7. 测试序号生成...")
        try:
            from datetime import datetime
            date_str = datetime.now().strftime('%Y%m%d')
            sequence = BatchNumberGenerator._get_daily_sequence(date_str, category.id, supplier.id)
            print(f"   ✅ 当日序号: {sequence}")
            
        except Exception as e:
            print(f"   ❌ 序号生成测试失败: {e}")
            return False
        
        # 8. 测试多次生成的唯一性
        print("\n8. 测试多次生成的唯一性...")
        try:
            batch_numbers = []
            for i in range(3):
                bn = generate_batch_number(category.id, supplier.id, user.id)
                batch_numbers.append(bn)
                print(f"   - 第{i+1}次生成: {bn}")
            
            # 检查是否有重复
            if len(set(batch_numbers)) == len(batch_numbers):
                print("   ✅ 多次生成的批次号都是唯一的")
            else:
                print("   ❌ 发现重复的批次号")
                return False
            
        except Exception as e:
            print(f"   ❌ 唯一性测试失败: {e}")
            return False
        
        # 9. 测试错误处理
        print("\n9. 测试错误处理...")
        try:
            # 测试无效的分类ID
            invalid_batch = generate_batch_number(99999, supplier.id, user.id)
            print(f"   ✅ 无效分类ID处理: {invalid_batch}")
            
            # 测试无效的供应商ID
            invalid_batch2 = generate_batch_number(category.id, 99999, user.id)
            print(f"   ✅ 无效供应商ID处理: {invalid_batch2}")
            
            # 测试无效批次号验证
            invalid_numbers = ['', 'ABC123', 'PB20240528', 'PB20240528VEG']
            for invalid in invalid_numbers:
                is_valid = validate_batch_number(invalid)
                print(f"   - '{invalid}' 验证结果: {'有效' if is_valid else '无效'}")
            
        except Exception as e:
            print(f"   ❌ 错误处理测试失败: {e}")
            return False
        
        # 10. 测试完整的批次创建流程
        print("\n10. 测试完整的批次创建流程...")
        try:
            # 模拟批次创建
            batch_number = generate_batch_number(category.id, supplier.id, user.id)
            display_name = BatchNumberGenerator.generate_display_name(
                batch_number, category.name, supplier.name
            )
            
            print(f"   ✅ 批次号: {batch_number}")
            print(f"   ✅ 显示名称: {display_name}")
            print(f"   ✅ 格式验证: {'通过' if validate_batch_number(batch_number) else '失败'}")
            
            # 解析验证
            parsed = parse_batch_number(batch_number)
            if parsed and parsed['supplier_id'] == supplier.id:
                print("   ✅ 解析验证: 供应商ID匹配")
            else:
                print("   ❌ 解析验证: 供应商ID不匹配")
                return False
            
        except Exception as e:
            print(f"   ❌ 完整流程测试失败: {e}")
            return False
        
        print("\n=== 测试完成 ===")
        print("✅ 所有测试通过！批次号生成器功能正常工作。")
        print(f"✅ 最终生成的批次号: {batch_number}")
        print(f"✅ 最终显示名称: {display_name}")
        return True

if __name__ == '__main__':
    success = test_batch_number_generator()
    sys.exit(0 if success else 1)
