-- 为 consumption_plans 表添加 area_id 字段
-- 这个脚本将安全地添加 area_id 字段，如果字段已存在则跳过

USE StudentsCMSSP;
GO

-- 检查字段是否已存在
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_plans' 
    AND COLUMN_NAME = 'area_id'
)
BEGIN
    -- 添加 area_id 字段
    ALTER TABLE consumption_plans 
    ADD area_id INT NULL;
    
    PRINT 'area_id 字段已添加到 consumption_plans 表';
    
    -- 添加外键约束（如果 administrative_areas 表存在）
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'administrative_areas')
    BEGIN
        ALTER TABLE consumption_plans 
        ADD CONSTRAINT FK_consumption_plans_area_id 
        FOREIGN KEY (area_id) REFERENCES administrative_areas(id);
        
        PRINT '外键约束已添加';
    END
    ELSE
    BEGIN
        PRINT '警告: administrative_areas 表不存在，跳过外键约束';
    END
    
    -- 尝试从关联的 menu_plans 表更新 area_id
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'menu_plans' AND COLUMN_NAME = 'area_id')
    BEGIN
        UPDATE cp 
        SET cp.area_id = mp.area_id
        FROM consumption_plans cp
        INNER JOIN menu_plans mp ON cp.menu_plan_id = mp.id
        WHERE cp.area_id IS NULL;
        
        DECLARE @updated_count INT = @@ROWCOUNT;
        PRINT CONCAT('已更新 ', @updated_count, ' 条记录的 area_id');
    END
    ELSE
    BEGIN
        PRINT '警告: menu_plans 表没有 area_id 字段，无法自动更新数据';
    END
END
ELSE
BEGIN
    PRINT 'area_id 字段已存在于 consumption_plans 表中';
END

-- 检查结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'consumption_plans' 
AND COLUMN_NAME = 'area_id';

PRINT '迁移完成！';
