-- =====================================================
-- 为 consumption_plans 表添加 area_id 字段的完整脚本
-- 执行前请备份数据库！
-- =====================================================

USE StudentsCMSSP;
GO

PRINT '开始执行 consumption_plans 表 area_id 字段添加脚本...';
PRINT '执行时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '=====================================================';

-- =====================================================
-- 第一步：检查并添加 area_id 字段
-- =====================================================
PRINT '第一步：检查并添加 area_id 字段';

IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_plans' 
    AND COLUMN_NAME = 'area_id'
)
BEGIN
    PRINT '正在添加 area_id 字段...';
    ALTER TABLE consumption_plans ADD area_id INT NULL;
    PRINT '✅ area_id 字段添加成功';
END
ELSE
BEGIN
    PRINT '⚠️  area_id 字段已存在，跳过添加步骤';
END

-- =====================================================
-- 第二步：数据迁移 - 从 menu_plans 更新 area_id
-- =====================================================
PRINT '';
PRINT '第二步：数据迁移';

-- 检查 menu_plans 表是否存在 area_id 字段
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'menu_plans' 
    AND COLUMN_NAME = 'area_id'
)
BEGIN
    PRINT '正在从 menu_plans 更新 consumption_plans 的 area_id...';
    
    -- 更新现有记录的 area_id
    UPDATE cp 
    SET cp.area_id = mp.area_id
    FROM consumption_plans cp
    INNER JOIN menu_plans mp ON cp.menu_plan_id = mp.id
    WHERE cp.area_id IS NULL AND mp.area_id IS NOT NULL;
    
    DECLARE @updated_count INT = @@ROWCOUNT;
    PRINT '✅ 已更新 ' + CAST(@updated_count AS VARCHAR) + ' 条记录的 area_id';
END
ELSE
BEGIN
    PRINT '⚠️  menu_plans 表没有 area_id 字段，跳过数据迁移';
END

-- =====================================================
-- 第三步：添加外键约束
-- =====================================================
PRINT '';
PRINT '第三步：添加外键约束';

-- 检查外键约束是否已存在
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS 
    WHERE CONSTRAINT_NAME = 'FK_consumption_plans_area_id'
)
BEGIN
    -- 检查 administrative_areas 表是否存在
    IF EXISTS (
        SELECT * FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_NAME = 'administrative_areas'
    )
    BEGIN
        PRINT '正在添加外键约束...';
        ALTER TABLE consumption_plans 
        ADD CONSTRAINT FK_consumption_plans_area_id 
        FOREIGN KEY (area_id) REFERENCES administrative_areas(id);
        PRINT '✅ 外键约束添加成功';
    END
    ELSE
    BEGIN
        PRINT '⚠️  administrative_areas 表不存在，跳过外键约束';
    END
END
ELSE
BEGIN
    PRINT '⚠️  外键约束已存在，跳过添加步骤';
END

-- =====================================================
-- 第四步：创建索引（可选，提升查询性能）
-- =====================================================
PRINT '';
PRINT '第四步：创建索引';

IF NOT EXISTS (
    SELECT * FROM sys.indexes 
    WHERE name = 'IX_consumption_plans_area_id' 
    AND object_id = OBJECT_ID('consumption_plans')
)
BEGIN
    PRINT '正在创建 area_id 索引...';
    CREATE INDEX IX_consumption_plans_area_id ON consumption_plans(area_id);
    PRINT '✅ 索引创建成功';
END
ELSE
BEGIN
    PRINT '⚠️  索引已存在，跳过创建步骤';
END

-- =====================================================
-- 第五步：验证结果
-- =====================================================
PRINT '';
PRINT '第五步：验证结果';
PRINT '=====================================================';

-- 检查字段信息
PRINT '字段信息：';
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '允许空值',
    COLUMN_DEFAULT as '默认值'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'consumption_plans' 
AND COLUMN_NAME = 'area_id';

-- 统计数据
PRINT '';
PRINT '数据统计：';
DECLARE @total_count INT, @with_area_id_count INT, @null_area_id_count INT;

SELECT @total_count = COUNT(*) FROM consumption_plans;
SELECT @with_area_id_count = COUNT(*) FROM consumption_plans WHERE area_id IS NOT NULL;
SELECT @null_area_id_count = COUNT(*) FROM consumption_plans WHERE area_id IS NULL;

PRINT '总记录数: ' + CAST(@total_count AS VARCHAR);
PRINT '有 area_id 的记录数: ' + CAST(@with_area_id_count AS VARCHAR);
PRINT 'area_id 为空的记录数: ' + CAST(@null_area_id_count AS VARCHAR);

-- 检查外键约束
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS 
    WHERE CONSTRAINT_NAME = 'FK_consumption_plans_area_id'
)
BEGIN
    PRINT '✅ 外键约束存在';
END
ELSE
BEGIN
    PRINT '⚠️  外键约束不存在';
END

-- 检查索引
IF EXISTS (
    SELECT * FROM sys.indexes 
    WHERE name = 'IX_consumption_plans_area_id' 
    AND object_id = OBJECT_ID('consumption_plans')
)
BEGIN
    PRINT '✅ 索引存在';
END
ELSE
BEGIN
    PRINT '⚠️  索引不存在';
END

PRINT '';
PRINT '=====================================================';
PRINT '脚本执行完成！';
PRINT '完成时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '=====================================================';

-- 显示一些示例数据（可选）
PRINT '';
PRINT '前5条记录示例：';
SELECT TOP 5 
    id,
    menu_plan_id,
    area_id,
    consumption_date,
    meal_type,
    status
FROM consumption_plans
ORDER BY id;

PRINT '';
PRINT '✅ 所有步骤执行完成，可以重启应用程序测试！';
