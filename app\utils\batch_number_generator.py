"""
产品批次号生成器
"""
import random
from datetime import datetime


class BatchNumberGenerator:
    """批次号生成器"""

    @staticmethod
    def generate_batch_number(category_id=None, supplier_id=None, user_id=None):
        """
        生成批次号
        格式：PB + YYYYMMDD + 6位随机数
        例如：PB20240528123456

        Args:
            category_id: 分类ID（保留参数，兼容性）
            supplier_id: 供应商ID（保留参数，兼容性）
            user_id: 用户ID（保留参数，兼容性）

        Returns:
            str: 生成的批次号
        """
        # 获取当前日期
        today = datetime.now()
        date_str = today.strftime('%Y%m%d')

        # 生成6位随机数
        random_num = random.randint(100000, 999999)

        # 组装批次号
        batch_number = f"PB{date_str}{random_num}"

        return batch_number

    @staticmethod
    def generate_display_name(batch_number, category_name=None, supplier_name=None):
        """
        生成批次显示名称

        Args:
            batch_number: 批次号
            category_name: 分类名称
            supplier_name: 供应商名称

        Returns:
            str: 显示名称
        """
        try:
            # 从批次号中提取日期：PB20240528123456
            if len(batch_number) >= 10:
                date_str = batch_number[2:10]  # 20240528
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                date_display = date_obj.strftime('%Y年%m月%d日')
            else:
                date_display = datetime.now().strftime('%Y年%m月%d日')

            # 构建显示名称
            parts = [date_display]

            if category_name:
                parts.append(category_name)

            if supplier_name:
                parts.append(f"({supplier_name})")

            parts.append("批次")

            return " ".join(parts)

        except Exception:
            return f"产品批次 {batch_number}"


def generate_batch_number(category_id=None, supplier_id=None, user_id=None):
    """
    便捷函数：生成批次号
    """
    return BatchNumberGenerator.generate_batch_number(category_id, supplier_id, user_id)
