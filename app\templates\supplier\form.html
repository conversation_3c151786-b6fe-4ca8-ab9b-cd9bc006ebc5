{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                </div>
                {% if title == '添加供应商' %}
                <div class="card-body bg-light">
                    <div class="alert alert-success mb-0">
                        <i class="fas fa-handshake"></i>
                        <strong>一步完成：</strong>填写供应商基本信息和学校合作关系，一次性完成供应商添加和学校绑定。
                    </div>
                </div>
                {% endif %}
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.name.label }}
                                    {{ form.name(class="form-control") }}
                                    {% for error in form.name.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.category_id.label }}
                                    {{ form.category_id(class="form-control") }}
                                    {% for error in form.category_id.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.contact_person.label }}
                                    {{ form.contact_person(class="form-control") }}
                                    {% for error in form.contact_person.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.phone.label }}
                                    {{ form.phone(class="form-control") }}
                                    {% for error in form.phone.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.email.label }}
                                    {{ form.email(class="form-control") }}
                                    {% for error in form.email.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.address.label }}
                                    {{ form.address(class="form-control") }}
                                    {% for error in form.address.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.business_license.label }}
                                    {{ form.business_license(class="form-control") }}
                                    {% for error in form.business_license.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.tax_id.label }}
                                    {{ form.tax_id(class="form-control") }}
                                    {% for error in form.tax_id.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.bank_name.label }}
                                    {{ form.bank_name(class="form-control") }}
                                    {% for error in form.bank_name.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.bank_account.label }}
                                    {{ form.bank_account(class="form-control") }}
                                    {% for error in form.bank_account.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.rating.label }}
                                    {{ form.rating(class="form-control") }}
                                    {% for error in form.rating.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                    <small class="form-text text-muted">评级范围：0-5，可以包含小数</small>
                                </div>
                                <div class="form-group">
                                    {{ form.status.label }}
                                    {{ form.status(class="form-control") }}
                                    {% for error in form.status.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>

                        {% if title == '添加供应商' %}
                        <!-- 学校合作关系信息 -->
                        <hr>
                        <h5 class="text-primary"><i class="fas fa-school"></i> 学校合作关系</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.area_id.label }}
                                    {{ form.area_id(class="form-control") }}
                                    {% for error in form.area_id.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.contract_number.label }}
                                    <div class="input-group">
                                        {{ form.contract_number(class="form-control", id="contract_number") }}
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-outline-secondary" id="generateContractBtn">
                                                <i class="fas fa-sync-alt"></i> 生成
                                            </button>
                                        </div>
                                    </div>
                                    <small class="text-muted">留空将自动生成</small>
                                    {% for error in form.contract_number.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.relation_status.label }}
                                    {{ form.relation_status(class="form-control") }}
                                    {% for error in form.relation_status.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.start_date.label }}
                                    {{ form.start_date(class="form-control datepicker") }}
                                    {% for error in form.start_date.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.end_date.label }}
                                    {{ form.end_date(class="form-control datepicker") }}
                                    <small class="text-muted">留空表示长期合作</small>
                                    {% for error in form.end_date.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.notes.label }}
                                    {{ form.notes(class="form-control", rows=3) }}
                                    {% for error in form.notes.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if title == '编辑供应商' and supplier %}
                        <!-- 学校合作关系信息（只读显示） -->
                        <hr>
                        <h5 class="text-info"><i class="fas fa-school"></i> 学校合作关系</h5>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>提示：</strong>学校合作关系请在"供应商-学校关系管理"页面进行修改。
                        </div>

                        {% if accessible_relations %}
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>合作学校</th>
                                        <th>合同编号</th>
                                        <th>合作期限</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for relation in accessible_relations %}
                                    <tr>
                                        <td>{{ relation.area.name }}</td>
                                        <td>{{ relation.contract_number }}</td>
                                        <td>
                                            {{ relation.start_date.strftime('%Y-%m-%d') if relation.start_date }}
                                            {% if relation.end_date %}
                                            至 {{ relation.end_date.strftime('%Y-%m-%d') }}
                                            {% else %}
                                            至 长期
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if relation.status == 1 %}
                                            <span class="badge badge-success">有效</span>
                                            {% else %}
                                            <span class="badge badge-secondary">已终止</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('supplier_school.edit', id=relation.id) }}"
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i> 编辑
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            该供应商在您管辖的学校中暂无合作关系。
                            <a href="{{ url_for('supplier_school.create', supplier_id=supplier.id) }}"
                               class="btn btn-sm btn-primary ml-2">
                                <i class="fas fa-plus"></i> 添加合作关系
                            </a>
                        </div>
                        {% endif %}
                        {% endif %}
                        <div class="form-group">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('supplier.index') }}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 日期选择器
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            todayHighlight: true,
            language: 'zh-CN'
        });

        // 生成合同编号函数
        function generateContractNumber() {
            // 获取当前日期
            var now = new Date();
            var year = now.getFullYear();
            var month = (now.getMonth() + 1).toString().padStart(2, '0');
            var day = now.getDate().toString().padStart(2, '0');

            // 生成随机数
            var randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

            // 组合合同编号：C + 年月日 + 4位随机数
            return 'C' + year + month + day + randomNum;
        }

        // 生成合同编号按钮点击事件
        $('#generateContractBtn').click(function() {
            $('#contract_number').val(generateContractNumber());
        });

        // 如果合同编号为空，自动生成一个
        if ($('#contract_number').length && !$('#contract_number').val()) {
            $('#contract_number').val(generateContractNumber());
        }

        // 供应商和学校选择变化时，可以考虑重新生成合同编号
        $('#area_id').change(function() {
            var areaId = $(this).val();
            if (areaId > 0) {
                var currentContractNumber = $('#contract_number').val();
                if (!currentContractNumber) {
                    $('#contract_number').val(generateContractNumber());
                }
            }
        });
    });
</script>
{% endblock %}