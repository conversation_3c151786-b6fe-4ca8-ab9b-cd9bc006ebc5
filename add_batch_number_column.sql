-- 添加批次号字段到产品批次表
-- 执行前请备份数据库

USE [your_database_name];  -- 请替换为实际的数据库名

-- 1. 添加 batch_number 字段
ALTER TABLE product_batches 
ADD batch_number NVARCHAR(50) NULL;

-- 2. 为现有记录生成批次号
-- 使用临时的批次号格式，避免重复
UPDATE product_batches 
SET batch_number = 'PB' + FORMAT(created_at, 'yyyyMMdd') + RIGHT('000000' + CAST(id AS VARCHAR), 6)
WHERE batch_number IS NULL;

-- 3. 设置字段为非空
ALTER TABLE product_batches 
ALTER COLUMN batch_number NVARCHAR(50) NOT NULL;

-- 4. 添加唯一约束
ALTER TABLE product_batches 
ADD CONSTRAINT UK_product_batches_batch_number UNIQUE (batch_number);

-- 5. 验证数据
SELECT TOP 10 
    id, 
    batch_number, 
    name, 
    created_at 
FROM product_batches 
ORDER BY created_at DESC;

PRINT '批次号字段添加完成！';
